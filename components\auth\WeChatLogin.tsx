import React from 'react';
import { ActivityIndicator, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { BrandBox } from '../ui/BrandBox';
interface WeChatLoginProps {
  isLoading: boolean;
  onWeChatLogin: () => void;
  onPhoneLogin: () => void;
}

export default function WeChatLogin({ isLoading, onWeChatLogin, onPhoneLogin }: WeChatLoginProps) {
  return (
    <View style={styles.wechatWrapper}>
      <View style={styles.header}>
        <BrandBox />
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.wechatButton, isLoading && styles.wechatButtonLoading]}
          onPress={onWeChatLogin}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <Icon name="wechat" size={24} color="#fff" style={styles.buttonIcon} />
              <Text style={styles.wechatButtonText}>微信登录</Text>
            </>
          )}
        </TouchableOpacity>

        <TouchableOpacity style={styles.phoneButton} onPress={onPhoneLogin}>
          <Icon name="phone-outline" size={20} color="#666" style={styles.buttonIcon} />
          <Text style={styles.phoneButtonText}>手机号登录</Text>
        </TouchableOpacity>
      </View>

    </View>
  );
}

const styles = StyleSheet.create({
  wechatWrapper: {
    backgroundColor:'green',
  },
  header: {
    backgroundColor:'red'
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: '#999',
  },
  wechatButton: {
    backgroundColor: '#07C160',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 40,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  wechatButtonLoading: {
    backgroundColor: '#05A050',
  },
  wechatButtonText: {
    color: '#fff',
    fontSize: 15,
    fontWeight: '600',
  },
  phoneButton: {
    backgroundColor: '#fff',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  phoneButtonText: {
    color: '#666',
    fontSize: 15,
    fontWeight: '600',
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonContainer:{
    marginTop:'50%'
  }
});
