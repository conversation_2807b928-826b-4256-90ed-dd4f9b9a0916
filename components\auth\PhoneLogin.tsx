import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React from 'react';
import { ActivityIndicator, Dimensions, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import {
  SafeAreaView,
} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
interface PhoneLoginProps {
  phone: string;
  isLoading: boolean;
  isPhoneValid: boolean;
  onPhoneChange: (phone: string) => void;
  onSendCode: () => void;
}

export default function PhoneLogin({
  phone,
  isLoading,
  isPhoneValid,
  onPhoneChange,
  onSendCode
}: PhoneLoginProps) {
  const router = useRouter();
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.headerContainer}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={36} color="#333" />
        </TouchableOpacity>
        <View style={styles.header}>
          <Text style={styles.title}>手机号登录</Text>
          <Text style={styles.subtitle}>未注册手机号验证后自动登录</Text>
        </View>
      </View>

      <View style={styles.inputContainer}>
        <Icon name="phone-outline" size={20} color="#666" style={styles.inputIcon} />
        <TextInput
          style={styles.input}
          placeholder="请输入手机号"
          value={phone}
          onChangeText={onPhoneChange}
          keyboardType="phone-pad"
          maxLength={11}
        />
      </View>

      <TouchableOpacity
        style={[styles.primaryButton, (isLoading || !isPhoneValid) && styles.primaryButtonDisabled]}
        onPress={onSendCode}
        disabled={isLoading || !isPhoneValid}
      >
        {isLoading ? (
          <ActivityIndicator size="small" color="#fff" />
        ) : (
          <Text style={styles.primaryButtonText}>发送验证码</Text>
        )}
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Math.max(30, screenHeight * 0.04),
  },
  container: {
    backgroundColor: '#FFFFFF',
    flex: 1,
    paddingHorizontal: Math.max(20, screenWidth * 0.05),
    paddingTop: Math.max(20, screenHeight * 0.02),
  },
  header: {
    flex: 1,
  },
  backButton: {
    padding: 8,
    marginRight: 12,
  },
  title: {
    fontSize: Math.max(22, screenWidth * 0.06),
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: Math.max(13, screenWidth * 0.035),
    color: '#999',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    paddingHorizontal: Math.max(16, screenWidth * 0.04),
    paddingVertical: Math.max(14, screenHeight * 0.018),
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#e9ecef',
    minHeight: Math.max(50, screenHeight * 0.06),
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: Math.max(15, screenWidth * 0.04),
    color: '#333',
    padding: 0,
  },
  primaryButton: {
    backgroundColor: '#3498db',
    paddingVertical: Math.max(16, screenHeight * 0.02),
    borderRadius: 12,
    alignItems: 'center',
    marginTop: Math.max(24, screenHeight * 0.03),
    minHeight: Math.max(50, screenHeight * 0.06),
    justifyContent: 'center',
    shadowColor: '#3498db',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  primaryButtonDisabled: {
    backgroundColor: '#bdc3c7',
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: Math.max(16, screenWidth * 0.045),
    fontWeight: '600',
  },
});
