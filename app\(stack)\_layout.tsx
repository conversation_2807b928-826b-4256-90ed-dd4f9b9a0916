import { Stack } from 'expo-router';
import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
export default function StackLayout() {
  return (
    <SafeAreaView>
      <Stack>
        <Stack.Screen
          name="(tabs)"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="edit-profile"
          options={{
            headerShown: true,
            headerTitle: "编辑资料",
            headerBackTitle: "返回",
            headerTintColor: '#333',
            headerTitleStyle: {
              fontWeight: '600',
            },
            headerStyle: {
              backgroundColor: '#fff',
            },
          }}
        />
        <Stack.Screen
          name="product-detail"
          options={{
            headerTitle: "产品详情",
            headerBackTitle: "返回",
          }}
        />
        <Stack.Screen
          name="brand-detail"
          options={{
            headerTitle: "品牌详情",
            headerBackTitle: "返回",
          }}
        />
        <Stack.Screen
          name="user-list"
          options={{
            headerTitle: "用户列表",
            headerBackTitle: "返回",
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="review-detail"
          options={{
            headerTitle: "回复详情",
            headerBackTitle: "返回",
          }}
        />
        <Stack.Screen
          name="all-popular-products"
          options={{
            headerTitle: "热门产品",
            headerBackTitle: "返回",
          }}
        />
        <Stack.Screen
          name="edit-review"
          options={{
            headerTitle: "编辑评论",
            headerBackTitle: "返回",
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="edit-thread"
          options={{
            headerTitle: "编辑帖子",
            headerBackTitle: "返回",
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="thread-detail"
          options={{
            headerTitle: "帖子详情",
            headerBackTitle: "返回",
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="search"
          options={{
            headerShown: false,
            animation: 'fade',
          }}
        />
        <Stack.Screen
          name="messages"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="chat"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="user-profile"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="settings"
          options={{
            headerShown: false,
          }}
        />
      </Stack>
    </SafeAreaView>
  );
}
