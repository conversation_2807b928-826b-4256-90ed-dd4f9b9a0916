import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import { Al<PERSON>, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import { useSignIn } from '@/store/userStore';
import AgreementCheckbox from '../../components/auth/AgreementCheckbox';
import AgreementModal from '../../components/auth/AgreementModal';
import EmailLogin from '../../components/auth/EmailLogin';
import PhoneLogin from '../../components/auth/PhoneLogin';
import WeChatLogin from '../../components/auth/WeChatLogin';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
type LoginType = 'wechat' | 'phone' | 'email';

export default function LoginScreen() {
  const router = useRouter();
  const [loginType, setLoginType] = useState<LoginType>('wechat');
  const [isLoading, setIsLoading] = useState(false);
  const [agreeTerms, setAgreeTerms] = useState(false);
  const [showAgreementModal, setShowAgreementModal] = useState(false);

  // Form state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [phone, setPhone] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  // Validation
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };


  const validatePhone = (phone: string) => {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  };

  const handleWeChatLogin = async () => {
    if (!agreeTerms) {
      setShowAgreementModal(true);
      return;
    }

    setIsLoading(true);
    try {
      // TODO: Implement WeChat login API
      Alert.alert('成功', '微信登录成功', [
        { text: '确定', onPress: () => router.replace('/(tabs)') }
      ]);
    } catch {
      Alert.alert('登录失败', '微信登录失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePhoneLogin = async () => {
    if (!agreeTerms) {
      setShowAgreementModal(true);
      return;
    }

    if (!validatePhone(phone)) {
      Alert.alert('错误', '请输入正确的手机号码');
      return;
    }

    setIsLoading(true);
    try {
      // TODO: Implement send verification code API
      Alert.alert('成功', '验证码已发送至您的手机', [
        { text: '确定', onPress: () => router.push(`/verify-code?phone=${phone}`) }
      ]);
    } catch {
      Alert.alert('错误', '发送验证码失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  const signInMutation = useSignIn();

  const handleEmailLogin = async () => {
    if (!agreeTerms) {
      setShowAgreementModal(true);
      return;
    }

    if (!validateEmail(email)) {
      Alert.alert('错误', '请输入正确的邮箱地址');
      return;
    }
    if (!password) {
      Alert.alert('错误', '请输入密码');
      return;
    }

    setIsLoading(true);
    try {
      await signInMutation.mutateAsync({
        email,
        password
      });
    } catch {
      // Error handling is done in the mutation
    } finally {
      setIsLoading(false);
    }
  };

  const handleAgreeAndLogin = () => {
    setAgreeTerms(true);
    setShowAgreementModal(false);

    setTimeout(() => {
      if (loginType === 'wechat') {
        handleWeChatLogin();
      } else if (loginType === 'phone') {
        handlePhoneLogin();
      } else {
        handleEmailLogin();
      }
    }, 100);
  };

  const renderLoginContent = () => {
    switch (loginType) {
      case 'wechat':
        return (
          <WeChatLogin
            isLoading={isLoading}
            onWeChatLogin={handleWeChatLogin}
            onPhoneLogin={() => setLoginType('phone')}
          />
        );
      case 'phone':
        return (
          <PhoneLogin
            phone={phone}
            isLoading={isLoading}
            isPhoneValid={validatePhone(phone)}
            onPhoneChange={setPhone}
            onSendCode={handlePhoneLogin}
          />
        );
      case 'email':
        return (
          <EmailLogin
            email={email}
            password={password}
            showPassword={showPassword}
            isLoading={isLoading}
            onEmailChange={setEmail}
            onPasswordChange={setPassword}
            onTogglePassword={() => setShowPassword(!showPassword)}
            onLogin={handleEmailLogin}
          />
        );
    }
  };

  return (
    <SafeAreaView>
      <StatusBar style="dark" />
      <View
        style={styles.container}
      >
        {renderLoginContent()}

        <AgreementCheckbox
          checked={agreeTerms}
          onToggle={() => setAgreeTerms(!agreeTerms)}
        />

        {/* Account Login Link at Bottom */}
        {loginType !== 'email' && (
          <TouchableOpacity
            style={styles.accountLoginContainer}
            onPress={() => setLoginType('email')}
          >
            <Text style={styles.accountLoginText}>账号密码登录</Text>
          </TouchableOpacity>
        )}
      </View>

      <AgreementModal
        visible={showAgreementModal}
        onCancel={() => setShowAgreementModal(false)}
        onConfirm={handleAgreeAndLogin}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  topBar: {
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 10,
  },
  closeButton: {
    padding: 4,
    alignSelf: 'flex-start',
  },
  container: {
    backgroundColor: 'red',
    height: 'auto',
    flex: 1,
    paddingHorizontal: 24,
  },
  accountLoginContainer: {
    alignItems: 'center',
    marginTop: 40,
    paddingVertical: 16,
  },
  accountLoginText: {
    color: '#3498db',
    fontSize: 14,
  },
});
